import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { Rate } from 'antd';
import { addAlpha, styled, t } from '@superset-ui/core';
import Button from 'src/components/Button';
import { TextArea } from 'src/components/Input';
import { RootState } from 'src/dashboard/types';
import Checkbox from 'src/components/Checkbox';
import Icons from 'src/components/Icons';
import { Tooltip } from 'src/components/Tooltip';
import Loading from 'src/components/Loading';

const CSI_LOCALSTORAGE_KEY = 'csiFormClosedDate';

const Container = styled.div<{ isFolded: boolean }>`
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 100;
  padding: 20px;
  width: 400px;
  max-width: 80vh;
  max-height: 80vh;
  background-color: ${({ theme }) => theme.colors.grayscale.light5};
  box-shadow: 0 2px 8px
    ${({ theme }) => addAlpha(theme.colors.grayscale.dark2, 0.2)};
  border: 1px solid ${({ theme }) => theme.colors.grayscale.light2};
  border-radius: 8px;
  transition: all 1s ease;
  overflow: hidden;
  ${({ isFolded }) => isFolded && 'max-width: 30px; max-height: 30px;'}
`;
const InnerContainer = styled.div<{ isFolded: boolean }>`
  min-width: 360px;
  opacity: ${({ isFolded }) => (isFolded ? 0 : 1)};
  transition: opacity 0.7s ease;
`;

const FoldButton = styled.span<{ isFolded: boolean }>`
  position: absolute;
  top: ${({ isFolded }) => (isFolded ? '6px' : '2px')};
  left: ${({ isFolded }) => (isFolded ? '10px' : '2px')};
  z-index: 1;
  cursor: pointer;
  color: ${({ theme }) => theme.colors.grayscale.base};
  rotate: ${({ isFolded }) => (isFolded ? '-135deg' : '45deg')};
  transition: all 0.7s ease;

  svg {
    font-size: ${({ isFolded }) => (isFolded ? '21px' : '16px')};
    transition: all 0.7s ease;
  }
`;

const Title = styled.p`
  margin-bottom: 16px;
`;

const FlexRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
`;

const RatingWrapper = styled(FlexRow)`
  margin-bottom: 16px;
`;

const StyledRate = styled(Rate)`
  font-size: 30px;
  color: ${({ theme }) => theme.colors.primary.base};
`;

const StyledTextArea = styled(TextArea)`
  margin-bottom: 16px;
  max-height: 40vh;
  resize: block;
`;

const StyledButton = styled(Button)`
  width: 100%;
`;

interface IContentProps {
  dashboardId: number;
  userId: number;
}

const CSIFormContent = ({ dashboardId, userId }: IContentProps) => {
  const [show, setShow] = useState(false);
  const [isFolded, setIsFolded] = useState(false);
  const [rating, setRating] = useState(0);
  const [comment, setComment] = useState('');
  const [isAnonymous, setIsAnonymous] = useState(true);

  useEffect(() => {
    setTimeout(() => {
      setShow(true);
    }, 1000);
  }, []);

  if (!show) return null;

  const toggleFolding = () => setIsFolded(prev => !prev);

  const handleClose = () => {
    localStorage.setItem(
      `${CSI_LOCALSTORAGE_KEY}_${dashboardId}`,
      new Date().toISOString(),
    );
    setShow(false);
  };

  const handleSubmit = () => {
    alert(`Rating: ${rating}\nComment: ${comment}`);
  };

  return (
    <Container isFolded={isFolded}>
      <FoldButton
        role="button"
        onClick={toggleFolding}
        tabIndex={0}
        isFolded={isFolded}
        aria-label={t('Fold')}
        title={t('Fold')}
      >
        <Icons.ArrowRightOutlined />
      </FoldButton>
      <InnerContainer isFolded={isFolded}>
        <Title>
          Please, rate this dashboard according to ease-to-use, data filling,
          easy-to-find data
        </Title>

        <RatingWrapper>
          <StyledRate value={rating} onChange={setRating} allowHalf />
          <Tooltip
            title={
              isAnonymous
                ? t('Your feedback will be saved anonymously ')
                : t('Your feedback will not be saved anonymously')
            }
            placement="topRight"
          >
            <div>
              {t('Anonymously')}{' '}
              <Checkbox
                checked={isAnonymous}
                onChange={val => setIsAnonymous(Boolean(val))}
                disabled
              />
            </div>
          </Tooltip>
        </RatingWrapper>

        <StyledTextArea
          rows={1}
          placeholder={t('Leave your comments here...')}
          value={comment}
          onChange={e => setComment(e.target.value)}
        />

        <FlexRow>
          <StyledButton onClick={handleClose} buttonStyle="tertiary">
            {t('Close')}
          </StyledButton>
          <StyledButton
            buttonStyle="primary"
            onClick={handleSubmit}
            disabled={!rating}
          >
            {t('Submit')}
          </StyledButton>
        </FlexRow>
      </InnerContainer>
      {isLoading && <Loading />}
    </Container>
  );
};

interface IProps {
  dashboardId: number | undefined;
}

const CSIForm = ({ dashboardId }: IProps) => {
  const userId = useSelector<RootState, number | undefined>(
    ({ user }) => user.userId,
  );

  if (!dashboardId || !userId) return null;

  const closedDate = localStorage.getItem(
    `${CSI_LOCALSTORAGE_KEY}_${dashboardId}`,
  );
  if (closedDate) {
    const closedDateObj = new Date(closedDate);
    const now = new Date();
    const diff = now.getTime() - closedDateObj.getTime();
    const diffInDays = Math.floor(diff / (1000 * 60 * 60 * 24));
    if (diffInDays < 1) return null;
    localStorage.removeItem(`${CSI_LOCALSTORAGE_KEY}_${dashboardId}`);
  }

  return <CSIFormContent dashboardId={dashboardId} userId={userId} />;
};

export default CSIForm;
